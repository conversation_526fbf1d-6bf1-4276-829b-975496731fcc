import { test, expect } from '@playwright/test';

test.describe('Layout Improvements - After 2025 Spacing Standards', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:4173');
    await page.waitForLoadState('networkidle');
    // Wait for animations to settle
    await page.waitForTimeout(1000);
  });

  test('Capture improved layout - Desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Full page screenshot
    await page.screenshot({ 
      path: 'tests/screenshots/after/desktop-full-page.png',
      fullPage: true 
    });

    // Individual sections with improved spacing
    await page.locator('#home').screenshot({ 
      path: 'tests/screenshots/after/desktop-hero.png' 
    });
    
    await page.locator('#services').screenshot({ 
      path: 'tests/screenshots/after/desktop-services.png' 
    });
    
    await page.locator('#portfolio').screenshot({ 
      path: 'tests/screenshots/after/desktop-portfolio.png' 
    });
    
    await page.locator('#contact').screenshot({ 
      path: 'tests/screenshots/after/desktop-contact.png' 
    });
    
    await page.locator('footer').screenshot({ 
      path: 'tests/screenshots/after/desktop-footer.png' 
    });
  });

  test('Capture improved layout - Tablet', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    
    await page.screenshot({ 
      path: 'tests/screenshots/after/tablet-full-page.png',
      fullPage: true 
    });

    await page.locator('#home').screenshot({ 
      path: 'tests/screenshots/after/tablet-hero.png' 
    });
    
    await page.locator('#services').screenshot({ 
      path: 'tests/screenshots/after/tablet-services.png' 
    });
    
    await page.locator('#portfolio').screenshot({ 
      path: 'tests/screenshots/after/tablet-portfolio.png' 
    });
  });

  test('Capture improved layout - Mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.screenshot({ 
      path: 'tests/screenshots/after/mobile-full-page.png',
      fullPage: true 
    });

    await page.locator('#home').screenshot({ 
      path: 'tests/screenshots/after/mobile-hero.png' 
    });
    
    await page.locator('#services').screenshot({ 
      path: 'tests/screenshots/after/mobile-services.png' 
    });
    
    await page.locator('#portfolio').screenshot({ 
      path: 'tests/screenshots/after/mobile-portfolio.png' 
    });
  });

  test('Verify improved button touch targets', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check button sizes meet 44px minimum
    const buttons = page.locator('.btn');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i);
      const box = await button.boundingBox();
      
      if (box) {
        console.log(`Button ${i + 1} size: ${box.width}x${box.height}`);
        expect(box.height).toBeGreaterThanOrEqual(44); // 44px minimum touch target
        expect(box.width).toBeGreaterThanOrEqual(44);
      }
    }
  });

  test('Verify improved spacing consistency', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Check section spacing
    const sections = ['#home', '#services', '#portfolio', '#contact'];
    const spacings = [];
    
    for (const selector of sections) {
      const section = page.locator(selector);
      const box = await section.boundingBox();
      if (box) {
        spacings.push(box.height);
        console.log(`Section ${selector} height: ${box.height}px`);
      }
    }
    
    // Verify sections have reasonable heights (not too cramped)
    for (const height of spacings) {
      expect(height).toBeGreaterThan(400); // Minimum section height
    }
  });

  test('Verify typography line heights', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Check heading line heights
    const headings = page.locator('h1, h2, h3');
    const headingCount = await headings.count();
    
    for (let i = 0; i < Math.min(headingCount, 3); i++) {
      const heading = headings.nth(i);
      const lineHeight = await heading.evaluate(el => 
        window.getComputedStyle(el).lineHeight
      );
      console.log(`Heading ${i + 1} line-height: ${lineHeight}`);
    }
    
    // Check body text line heights
    const paragraphs = page.locator('p');
    const paragraphCount = await paragraphs.count();
    
    for (let i = 0; i < Math.min(paragraphCount, 3); i++) {
      const paragraph = paragraphs.nth(i);
      const lineHeight = await paragraph.evaluate(el => 
        window.getComputedStyle(el).lineHeight
      );
      console.log(`Paragraph ${i + 1} line-height: ${lineHeight}`);
    }
  });

  test('Verify grid spacing improvements', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Check services grid
    const servicesGrid = page.locator('#services .grid-brutal');
    const servicesBox = await servicesGrid.boundingBox();
    
    if (servicesBox) {
      console.log(`Services grid dimensions: ${servicesBox.width}x${servicesBox.height}`);
    }
    
    // Check portfolio grid
    const portfolioGrid = page.locator('#portfolio .grid-brutal');
    const portfolioBox = await portfolioGrid.boundingBox();
    
    if (portfolioBox) {
      console.log(`Portfolio grid dimensions: ${portfolioBox.width}x${portfolioBox.height}`);
    }
  });
});
