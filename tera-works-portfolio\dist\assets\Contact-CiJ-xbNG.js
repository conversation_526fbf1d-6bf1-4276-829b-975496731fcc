import{S as b,j as e}from"./index-D-MEtesh.js";import{r as a,P as N,a as w,b as k,m as C,n as S,o as g,p as T}from"./ui-DOs47f7g.js";import{g as i}from"./gsap-CH_iu5NA.js";import"./vendor-1zw1pNgy.js";i.registerPlugin(b);const I=()=>{const c=a.useRef(null),d=a.useRef(null),m=a.useRef(null),[r,x]=a.useState({name:"",email:"",phone:"",service:"",message:""}),[p,h]=a.useState(!1),[y,u]=a.useState(!1),f=["Web Development","Social Media Management","Brand Development","Mobile App Development","SEO Optimization","Digital Marketing","Other"];a.useEffect(()=>{const s=c.current,t=d.current,o=m.current;if(!(!s||!t||!o))return i.fromTo(t.children,{opacity:0,y:50},{opacity:1,y:0,duration:1,stagger:.2,ease:"power3.out",scrollTrigger:{trigger:t,start:"top 80%",end:"bottom 20%",toggleActions:"play none none reverse"}}),i.fromTo(o.children,{opacity:0,y:80},{opacity:1,y:0,duration:.8,stagger:.15,ease:"power3.out",scrollTrigger:{trigger:o,start:"top 85%",end:"bottom 15%",toggleActions:"play none none reverse"}}),()=>{b.getAll().forEach(n=>n.kill())}},[]);const l=s=>{const{name:t,value:o}=s.target;x(n=>({...n,[t]:o}))},v=async s=>{s.preventDefault(),h(!0),await new Promise(t=>setTimeout(t,2e3)),h(!1),u(!0),setTimeout(()=>{u(!1),x({name:"",email:"",phone:"",service:"",message:""})},3e3)},j=()=>{const s=encodeURIComponent(`Hi! I'm interested in your services. ${r.name?`My name is ${r.name}.`:""} ${r.service?`I'm looking for ${r.service} services.`:""}`);window.open(`https://wa.me/94715768552?text=${s}`,"_blank")};return e.jsx("section",{ref:c,id:"contact",className:"section-padding",style:{backgroundColor:"var(--bg-secondary)"},children:e.jsxs("div",{className:"container-custom",children:[e.jsxs("div",{ref:d,className:"space-y-8 md:space-y-12 lg:space-y-16 mb-16 md:mb-20 lg:mb-24",children:[e.jsx("div",{className:"inline-block p-4 border-4 border-primary",style:{borderColor:"var(--color-primary)",backgroundColor:"var(--color-primary)"},children:e.jsx("span",{className:"text-black font-bold text-lg mono uppercase tracking-wide",children:"CONTACT"})}),e.jsxs("h2",{className:"text-6xl md:text-8xl lg:text-9xl font-bold leading-none",style:{fontFamily:"Space Grotesk, sans-serif",color:"var(--text-primary)",lineHeight:"var(--leading-none)"},children:["LET'S",e.jsx("br",{}),e.jsx("span",{className:"gradient-text",children:"TALK"})]}),e.jsx("div",{className:"max-w-4xl",children:e.jsxs("p",{className:"text-xl md:text-2xl lg:text-3xl font-medium",style:{color:"var(--text-secondary)",lineHeight:"var(--leading-normal)"},children:["READY TO STOP WASTING MONEY ON BAD WEBSITES?",e.jsx("br",{}),"LET'S BUILD SOMETHING THAT ACTUALLY CONVERTS."]})})]}),e.jsxs("div",{ref:m,className:"grid-asymmetric gap-12",children:[e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"card card-padding",children:[e.jsx("h3",{className:"text-3xl font-bold mb-8 uppercase",style:{fontFamily:"Space Grotesk, sans-serif",color:"var(--text-primary)"},children:"GET IN TOUCH"}),e.jsxs("div",{className:"space-y-8 md:space-y-10 lg:space-y-12",children:[e.jsxs("div",{className:"flex items-start space-x-6 md:space-x-8 group",children:[e.jsx("div",{className:"w-16 h-16 md:w-20 md:h-20 border-4 border-primary flex items-center justify-center group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200",style:{borderColor:"var(--color-primary)",backgroundColor:"var(--color-primary)"},children:e.jsx(N,{className:"text-black",size:24})}),e.jsxs("div",{className:"space-y-2 md:space-y-3",children:[e.jsx("p",{className:"font-bold text-base md:text-lg mono uppercase tracking-wide",style:{color:"var(--text-primary)"},children:"PHONE"}),e.jsx("a",{href:"tel:+94715768552",className:"text-xl md:text-2xl font-bold mono hover:translate-x-1 transition-transform duration-200 inline-block",style:{color:"var(--text-primary)"},children:"+94 71 576 8552"})]})]}),e.jsxs("div",{className:"flex items-start space-x-6 md:space-x-8 group",children:[e.jsx("div",{className:"w-16 h-16 md:w-20 md:h-20 border-4 border-secondary flex items-center justify-center group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200",style:{borderColor:"var(--color-secondary)",backgroundColor:"var(--color-secondary)"},children:e.jsx(w,{className:"text-black",size:24})}),e.jsxs("div",{className:"space-y-2 md:space-y-3",children:[e.jsx("p",{className:"font-bold text-base md:text-lg mono uppercase tracking-wide",style:{color:"var(--text-primary)"},children:"EMAIL"}),e.jsx("a",{href:"mailto:<EMAIL>",className:"text-xl md:text-2xl font-bold mono hover:translate-x-1 transition-transform duration-200 inline-block",style:{color:"var(--text-primary)"},children:"<EMAIL>"})]})]}),e.jsxs("div",{className:"flex items-start space-x-6 md:space-x-8 group",children:[e.jsx("div",{className:"w-16 h-16 md:w-20 md:h-20 border-4 border-accent flex items-center justify-center group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200",style:{borderColor:"var(--color-accent)",backgroundColor:"var(--color-accent)"},children:e.jsx(k,{className:"text-black",size:24})}),e.jsxs("div",{className:"space-y-2 md:space-y-3",children:[e.jsx("p",{className:"font-bold text-base md:text-lg mono uppercase tracking-wide",style:{color:"var(--text-primary)"},children:"LOCATION"}),e.jsx("p",{className:"text-xl md:text-2xl font-bold mono",style:{color:"var(--text-primary)"},children:"COLOMBO, SRI LANKA"})]})]}),e.jsxs("div",{className:"flex items-center space-x-6 group",children:[e.jsx("div",{className:"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:e.jsx(C,{className:"text-yellow-300",size:24})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-white mb-1",children:"Business Hours"}),e.jsx("p",{className:"text-white/80 text-lg",children:"Mon - Fri: 9:00 AM - 6:00 PM"})]})]})]}),e.jsx("div",{className:"mt-10 pt-8 border-t border-white/20",children:e.jsxs("button",{onClick:j,className:"w-full btn btn-lg bg-green-600 hover:bg-green-700 text-white border-none","aria-label":"Contact us on WhatsApp - Contact Section",children:[e.jsx(S,{size:20}),"Chat on WhatsApp"]})})]}),e.jsxs("div",{className:"glass rounded-3xl card-padding",children:[e.jsx("h4",{className:"text-xl font-semibold mb-4 text-white",style:{fontFamily:"Poppins, sans-serif"},children:"Quick Response Guarantee"}),e.jsx("p",{className:"text-white/90 mb-6 text-lg leading-relaxed",children:"We respond to all inquiries within 2 hours during business hours."}),e.jsxs("div",{className:"flex items-center text-white/90",children:[e.jsx(g,{size:20,className:"mr-3 text-green-400"}),e.jsx("span",{className:"font-medium",children:"Average response time: 30 minutes"})]})]})]}),e.jsxs("div",{className:"glass rounded-3xl card-padding",children:[e.jsx("h3",{className:"text-2xl font-bold mb-8 text-white",style:{fontFamily:"Poppins, sans-serif"},children:"Send us a Message"}),y?e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(g,{className:"text-green-600",size:32})}),e.jsx("h4",{className:"text-xl font-semibold text-green-600 mb-2",children:"Message Sent!"}),e.jsx("p",{className:"text-gray-600",children:"Thank you for reaching out. We'll get back to you within 2 hours."})]}):e.jsxs("form",{onSubmit:v,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-white mb-2",children:"Full Name *"}),e.jsx("input",{type:"text",id:"name",name:"name",value:r.name,onChange:l,required:!0,className:"w-full px-4 py-3 border border-white/20 bg-white/10 backdrop-blur-sm rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-colors text-white placeholder-white/60",placeholder:"Your full name"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-white mb-2",children:"Email Address *"}),e.jsx("input",{type:"email",id:"email",name:"email",value:r.email,onChange:l,required:!0,className:"w-full px-4 py-3 border border-white/20 bg-white/10 backdrop-blur-sm rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-colors text-white placeholder-white/60",placeholder:"<EMAIL>"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:r.phone,onChange:l,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",placeholder:"+94 XX XXX XXXX"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"service",className:"block text-sm font-medium text-gray-700 mb-2",children:"Service Interested In"}),e.jsxs("select",{id:"service",name:"service",value:r.service,onChange:l,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",children:[e.jsx("option",{value:"",children:"Select a service"}),f.map(s=>e.jsx("option",{value:s,children:s},s))]})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),e.jsx("textarea",{id:"message",name:"message",value:r.message,onChange:l,required:!0,rows:5,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors resize-none",placeholder:"Tell us about your project..."})]}),e.jsxs("button",{type:"submit",disabled:p,className:"w-full btn btn-primary",children:[e.jsx(T,{size:20,className:"mr-2"}),p?"Sending...":"Send Message"]})]})]})]})]})})};export{I as default};
