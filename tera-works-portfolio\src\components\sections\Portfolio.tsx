import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ExternalLink, Monitor, Smartphone, Eye, X } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

interface Project {
  id: string;
  title: string;
  category: string;
  description: string;
  url: string;
  image: string;
  mobileImage: string;
  technologies: string[];
  features: string[];
  color: string;
}

const Portfolio = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const projectsRef = useRef<HTMLDivElement>(null);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile'>('desktop');

  const projects: Project[] = [
    {
      id: 'chalaka-dulanga',
      title: 'Chalaka Dulanga Photography',
      category: 'Photography Portfolio',
      description: 'A stunning fine art wedding photography portfolio showcasing breathtaking moments and artistic vision. Built with modern web technologies to create an immersive visual experience.',
      url: 'https://chalakadulangaphotography.com',
      image: '/screenshots/chalaka-dulanga-photography-preview.png',
      mobileImage: '/screenshots/chalaka-dulanga-photography-mobile.png',
      technologies: ['React', 'Next.js', 'Tailwind CSS', 'Framer Motion'],
      features: ['Responsive Gallery', 'Image Optimization', 'Contact Forms', 'SEO Optimized'],
      color: 'from-rose-500 to-pink-500'
    },
    {
      id: 'zeynthra',
      title: 'Zeynthra',
      category: 'Music & Entertainment',
      description: 'A dynamic progressive music DJ portfolio featuring interactive elements, music streaming integration, and event management capabilities.',
      url: 'https://zeynthra.com',
      image: '/screenshots/zeynthra-preview.png',
      mobileImage: '/screenshots/zeynthra-mobile.png',
      technologies: ['React', 'TypeScript', 'GSAP', 'Web Audio API'],
      features: ['Music Player', 'Event Calendar', 'Social Integration', 'Mobile Optimized'],
      color: 'from-purple-500 to-indigo-500'
    },
    {
      id: 'spice-herb',
      title: 'Spice & Herb Restaurant',
      category: 'Restaurant & Hospitality',
      description: 'An elegant restaurant website showcasing authentic Sri Lankan cuisine with online reservation system and menu management.',
      url: 'https://spiceandherbrestaurant.com',
      image: '/screenshots/spice-herb-restaurant-preview.png',
      mobileImage: '/screenshots/spice-herb-restaurant-mobile.png',
      technologies: ['WordPress', 'Custom PHP', 'MySQL', 'Stripe API'],
      features: ['Online Reservations', 'Menu Management', 'Payment Integration', 'Multi-language'],
      color: 'from-orange-500 to-red-500'
    }
  ];

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    const projectsContainer = projectsRef.current;

    if (!section || !title || !projectsContainer) return;

    // Animate title
    gsap.fromTo(title.children,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        stagger: 0.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: title,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Animate project cards
    gsap.fromTo(projectsContainer.children,
      { opacity: 0, y: 100, rotationY: 15 },
      {
        opacity: 1,
        y: 0,
        rotationY: 0,
        duration: 1,
        stagger: 0.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: projectsContainer,
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Cleanup
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const openProjectModal = (project: Project) => {
    setSelectedProject(project);
    setViewMode('desktop');
  };

  const closeProjectModal = () => {
    setSelectedProject(null);
  };

  return (
    <section ref={sectionRef} id="portfolio" className="section-padding" style={{ backgroundColor: 'var(--bg-primary)' }}>
      <div className="container-custom">
        <div ref={titleRef} className="space-y-8 md:space-y-12 lg:space-y-16 mb-16 md:mb-20 lg:mb-24">
          <div className="inline-block p-4 border-4 border-accent" style={{ borderColor: 'var(--color-accent)', backgroundColor: 'var(--color-accent)' }}>
            <span className="text-black font-bold text-lg mono uppercase tracking-wide">
              PORTFOLIO
            </span>
          </div>
          <h2 className="text-6xl md:text-8xl lg:text-9xl font-bold leading-none" style={{ fontFamily: 'Space Grotesk, sans-serif', color: 'var(--text-primary)', lineHeight: 'var(--leading-none)' }}>
            WORK THAT
            <br />
            <span className="gradient-text">WORKS</span>
          </h2>
          <div className="max-w-4xl">
            <p className="text-xl md:text-2xl lg:text-3xl font-medium" style={{ color: 'var(--text-secondary)', lineHeight: 'var(--leading-normal)' }}>
              REAL PROJECTS. REAL RESULTS. REAL CLIENTS WHO ACTUALLY MADE MONEY.
              <br />
              NO FAKE PORTFOLIO PIECES HERE.
            </p>
          </div>
        </div>

        <div ref={projectsRef} className="grid-brutal">
          {projects.map((project, index) => {
            const colors = [
              { bg: 'var(--color-primary)', border: 'var(--color-primary)' },
              { bg: 'var(--color-secondary)', border: 'var(--color-secondary)' },
              { bg: 'var(--color-accent)', border: 'var(--color-accent)' }
            ];
            const color = colors[index % colors.length];

            return (
              <div key={project.id} className="card overflow-hidden group">
                <div className="relative">
                  <div className="h-96 relative overflow-hidden border-b-4" style={{ borderColor: color.border }}>
                    <img
                      src={project.image}
                      alt={project.title}
                      loading="lazy"
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    {/* Brutalist Overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>

                    {/* Project Number */}
                    <div
                      className="absolute top-6 left-6 w-16 h-16 border-4 flex items-center justify-center"
                      style={{ borderColor: color.border, backgroundColor: color.bg }}
                    >
                      <span className="text-black font-bold mono text-lg">{String(index + 1).padStart(2, '0')}</span>
                    </div>
                  </div>

                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-all duration-300 flex space-x-4">
                      <button
                        className="btn btn-sm"
                        style={{ backgroundColor: color.bg, borderColor: color.border, color: 'black' }}
                        onClick={() => openProjectModal(project)}
                      >
                        <Eye size={16} />
                        VIEW
                      </button>
                      <button
                        className="btn btn-outline btn-sm"
                        onClick={() => window.open(project.url, '_blank')}
                      >
                        <ExternalLink size={16} />
                        VISIT
                      </button>
                    </div>
                  </div>
                </div>

              <div className="card-padding flex flex-col h-full">
                <div className="flex items-center gap-3 mb-6">
                  <div
                    className="px-3 py-2 border-2"
                    style={{ borderColor: color.border, backgroundColor: color.bg }}
                  >
                    <span className="text-black font-bold text-sm mono uppercase tracking-wide">
                      {project.category}
                    </span>
                  </div>
                </div>

                <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6 uppercase" style={{ fontFamily: 'Space Grotesk, sans-serif', color: 'var(--text-primary)', lineHeight: 'var(--leading-tight)' }}>
                  {project.title}
                </h3>

                <p className="mb-6 md:mb-8 text-lg md:text-xl line-clamp-3 font-medium flex-grow" style={{ color: 'var(--text-secondary)', lineHeight: 'var(--leading-normal)' }}>
                  {project.description}
                </p>

                <div className="flex flex-wrap gap-2 md:gap-3 mb-6 md:mb-8">
                  {project.technologies.slice(0, 3).map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-2 md:px-3 py-1 md:py-2 text-xs md:text-sm mono uppercase tracking-wide border-2"
                      style={{ borderColor: 'var(--border-color)', color: 'var(--text-muted)' }}
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="px-2 md:px-3 py-1 md:py-2 text-xs md:text-sm mono uppercase tracking-wide border-2" style={{ borderColor: 'var(--border-color)', color: 'var(--text-muted)' }}>
                      +{project.technologies.length - 3}
                    </span>
                  )}
                </div>

                <button
                  className="btn btn-outline w-full mt-auto"
                  onClick={() => openProjectModal(project)}
                >
                  VIEW DETAILS
                </button>
              </div>
            </div>
          );
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-20">
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Ready to Start Your Next Project?
          </h3>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Let's create something amazing together. Contact us to discuss your project requirements 
            and see how we can bring your vision to life.
          </p>
          <button className="btn btn-primary" onClick={() => window.open('https://wa.me/94715768552', '_blank')}>
            Start Your Project
          </button>
        </div>
      </div>

      {/* Project Modal */}
      {selectedProject && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200 flex items-center justify-between">
              <div>
                <h3 className="text-2xl font-bold">{selectedProject.title}</h3>
                <p className="text-gray-600">{selectedProject.category}</p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode('desktop')}
                    className={`p-2 rounded ${viewMode === 'desktop' ? 'bg-white shadow-sm' : ''}`}
                  >
                    <Monitor size={20} />
                  </button>
                  <button
                    onClick={() => setViewMode('mobile')}
                    className={`p-2 rounded ${viewMode === 'mobile' ? 'bg-white shadow-sm' : ''}`}
                  >
                    <Smartphone size={20} />
                  </button>
                </div>
                <button
                  onClick={closeProjectModal}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X size={24} />
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <div className="bg-gray-100 rounded-lg p-4 mb-6">
                    <img
                      src={viewMode === 'desktop' ? selectedProject.image : selectedProject.mobileImage}
                      alt={selectedProject.title}
                      loading="lazy"
                      className="w-full rounded-lg shadow-lg"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300"><rect width="400" height="300" fill="%23f3f4f6"/><text x="200" y="150" text-anchor="middle" fill="%236b7280" font-family="Arial" font-size="16">Preview not available</text></svg>`;
                      }}
                    />
                  </div>
                  
                  <div className="flex space-x-4">
                    <button
                      onClick={() => window.open(selectedProject.url, '_blank')}
                      className="flex-1 btn btn-primary"
                    >
                      <ExternalLink size={16} className="mr-2" />
                      Visit Website
                    </button>
                    <button
                      onClick={() => window.open('https://wa.me/94715768552', '_blank')}
                      className="flex-1 btn btn-outline"
                    >
                      Discuss Project
                    </button>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg font-semibold mb-4">Project Overview</h4>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {selectedProject.description}
                  </p>
                  
                  <h4 className="text-lg font-semibold mb-4">Technologies Used</h4>
                  <div className="flex flex-wrap gap-2 mb-6">
                    {selectedProject.technologies.map((tech, index) => (
                      <span 
                        key={index}
                        className="px-3 py-1 bg-primary/10 text-primary text-sm rounded-full"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                  
                  <h4 className="text-lg font-semibold mb-4">Key Features</h4>
                  <ul className="space-y-2">
                    {selectedProject.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-gray-600">
                        <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default Portfolio;
