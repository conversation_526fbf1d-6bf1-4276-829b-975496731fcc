@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 2025 Neo-Brutalist Design System - Dark Mode First */
:root {
  /* 2025 Dark Mode Color Palette */
  --color-primary: #00ff88; /* Electric green */
  --color-primary-dark: #00cc6a;
  --color-primary-light: #33ff99;
  --color-secondary: #ff0066; /* Hot pink */
  --color-accent: #ffff00; /* Electric yellow */
  --color-accent-dark: #cccc00;
  --color-warning: #ff6600; /* Electric orange */
  --color-error: #ff0033;
  --color-info: #0099ff;

  /* Dark Mode Background System */
  --bg-primary: #0a0a0a; /* Pure black */
  --bg-secondary: #111111; /* Dark gray */
  --bg-tertiary: #1a1a1a; /* Lighter dark */
  --bg-card: #151515; /* Card background */
  --bg-glass: rgba(255, 255, 255, 0.05); /* Subtle glass */
  --bg-overlay: rgba(0, 0, 0, 0.8);
  --bg-noise: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.02'/%3E%3C/svg%3E");

  /* Dark Mode Text System */
  --text-primary: #ffffff; /* Pure white */
  --text-secondary: #cccccc; /* Light gray */
  --text-muted: #888888; /* Medium gray */
  --text-inverse: #000000; /* Black for light backgrounds */
  --text-accent: var(--color-primary);

  /* Neo-Brutalist Border System */
  --border-color: #333333;
  --border-color-light: #444444;
  --border-color-dark: #222222;
  --border-radius-none: 0;
  --border-radius-sm: 2px;
  --border-radius: 4px;
  --border-radius-lg: 8px;
  --border-width: 2px;
  --border-width-thick: 4px;

  /* Neo-Brutalist Shadow System */
  --shadow-brutal: 4px 4px 0px var(--color-primary);
  --shadow-brutal-lg: 8px 8px 0px var(--color-primary);
  --shadow-brutal-xl: 12px 12px 0px var(--color-primary);
  --shadow-brutal-secondary: 4px 4px 0px var(--color-secondary);
  --shadow-brutal-accent: 4px 4px 0px var(--color-accent);
  --shadow-inset: inset 2px 2px 4px rgba(0, 0, 0, 0.3);

  /* Modular Spacing Scale - 2025 Best Practices (8px base unit) */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px - Base unit */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px - Mobile minimum */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px - Component spacing minimum */
  --space-7: 1.75rem;   /* 28px */
  --space-8: 2rem;      /* 32px - Desktop minimum */
  --space-9: 2.25rem;   /* 36px */
  --space-10: 2.5rem;   /* 40px */
  --space-11: 2.75rem;  /* 44px - Touch target minimum */
  --space-12: 3rem;     /* 48px */
  --space-14: 3.5rem;   /* 56px */
  --space-16: 4rem;     /* 64px */
  --space-18: 4.5rem;   /* 72px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-28: 7rem;     /* 112px */
  --space-32: 8rem;     /* 128px */
  --space-40: 10rem;    /* 160px */
  --space-48: 12rem;    /* 192px */
  --space-64: 16rem;    /* 256px */

  /* Bold Typography Scale - 2025 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 2rem;      /* 32px */
  --font-size-4xl: 2.5rem;    /* 40px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 4rem;      /* 64px */
  --font-size-7xl: 5rem;      /* 80px */
  --font-size-8xl: 6rem;      /* 96px */
  --font-size-9xl: 8rem;      /* 128px */

  /* Optimized Line Heights - 2025 Readability Standards */
  --leading-none: 1;        /* Headings only */
  --leading-tight: 1.1;     /* Large headings */
  --leading-snug: 1.2;      /* Subheadings */
  --leading-normal: 1.5;    /* Body text optimal */
  --leading-relaxed: 1.6;   /* Long-form content */
  --leading-loose: 1.8;     /* Accessibility enhanced */

  /* Snappy Transitions */
  --transition-instant: 100ms ease-out;
  --transition-fast: 200ms ease-out;
  --transition-base: 300ms ease-out;
  --transition-slow: 500ms ease-out;
  --transition-brutal: 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* Z-Index Scale */
  --z-base: 1;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-tooltip: 1070;
}

@layer base {
  /* Neo-Brutalist CSS Reset - 2025 */
  *, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: var(--leading-normal);
    text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
  }

  body {
    font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    background-image: var(--bg-noise);
    color: var(--text-primary);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    overflow-x: hidden;
    min-height: 100vh;
    font-size: var(--font-size-base);
    line-height: var(--leading-normal);
    position: relative;
  }

  /* Add subtle grain texture */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-noise);
    opacity: 0.03;
    pointer-events: none;
    z-index: 1;
  }

  /* Brutalist Scrollbar */
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border: var(--border-width) solid var(--border-color);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border: var(--border-width) solid var(--bg-primary);
    box-shadow: var(--shadow-brutal);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary-light);
    box-shadow: var(--shadow-brutal-lg);
  }

  ::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
  }

  /* Bold Typography Hierarchy - Neo-Brutalist */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    line-height: var(--leading-tight);
    color: var(--text-primary);
    letter-spacing: -0.02em;
    text-transform: uppercase;
  }

  h1 {
    font-size: clamp(var(--font-size-5xl), 8vw, var(--font-size-9xl));
    font-weight: 700;
    letter-spacing: -0.04em;
    line-height: var(--leading-none);
  }
  h2 {
    font-size: clamp(var(--font-size-4xl), 6vw, var(--font-size-7xl));
    font-weight: 700;
    line-height: var(--leading-tight);
  }
  h3 {
    font-size: clamp(var(--font-size-3xl), 4vw, var(--font-size-5xl));
    font-weight: 600;
  }
  h4 {
    font-size: clamp(var(--font-size-2xl), 3vw, var(--font-size-4xl));
    font-weight: 600;
  }
  h5 {
    font-size: var(--font-size-xl);
    font-weight: 500;
  }
  h6 {
    font-size: var(--font-size-lg);
    font-weight: 500;
  }

  p {
    line-height: var(--leading-normal);
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    font-weight: 400;
  }

  /* Monospace for accents */
  .mono {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 400;
  }

  a {
    color: var(--color-primary);
    text-decoration: none;
    transition: var(--transition-fast);
    outline: none;
  }

  a:hover {
    color: var(--color-primary-dark);
  }

  a:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    border-radius: var(--border-radius-sm);
  }

  /* Modern Form Elements */
  input, textarea, select, button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }

  button {
    cursor: pointer;
    border: none;
    background: none;
    outline: none;
  }

  button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  /* Modern Image Handling */
  img, picture, video, canvas, svg {
    display: block;
    max-width: 100%;
    height: auto;
  }

  /* Modern List Styling */
  ul, ol {
    list-style: none;
  }

  /* Modern Table Styling */
  table {
    border-collapse: collapse;
    border-spacing: 0;
  }
}

@layer components {
  /* Brutalist Container System */
  .container-custom {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    width: 100%;
    position: relative;
    z-index: var(--z-base);
  }

  @media (min-width: 640px) {
    .container-custom {
      padding: 0 var(--space-6);
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding: 0 var(--space-8);
    }
  }

  @media (min-width: 1280px) {
    .container-custom {
      padding: 0 var(--space-12);
    }
  }

  /* Section Spacing - 2025 Vertical Rhythm Standards */
  .section-padding {
    padding: var(--space-16) 0;  /* 64px mobile */
  }

  @media (min-width: 640px) {
    .section-padding {
      padding: var(--space-20) 0;  /* 80px tablet */
    }
  }

  @media (min-width: 768px) {
    .section-padding {
      padding: var(--space-24) 0;  /* 96px small desktop */
    }
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding: var(--space-32) 0;  /* 128px large desktop */
    }
  }

  @media (min-width: 1280px) {
    .section-padding {
      padding: var(--space-40) 0;  /* 160px extra large */
    }
  }

  /* Brutalist Button System - 2025 Touch Standards */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-3);
    padding: var(--space-4) var(--space-8);
    min-height: var(--space-11);  /* 44px minimum touch target */
    min-width: var(--space-11);
    border: var(--border-width) solid var(--border-color);
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 600;
    font-size: var(--font-size-base);
    line-height: var(--leading-normal);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: var(--transition-brutal);
    cursor: pointer;
    text-decoration: none;
    position: relative;
    white-space: nowrap;
    user-select: none;
    outline: none;
    background: var(--bg-card);
    color: var(--text-primary);
  }

  .btn:focus-visible {
    outline: var(--border-width) solid var(--color-primary);
    outline-offset: 2px;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }

  /* Primary Button - Brutalist */
  .btn-primary {
    background: var(--color-primary);
    color: var(--bg-primary);
    border-color: var(--color-primary);
    box-shadow: var(--shadow-brutal);
  }

  .btn-primary:hover:not(:disabled) {
    transform: translate(-2px, -2px);
    box-shadow: var(--shadow-brutal-lg);
  }

  .btn-primary:active {
    transform: translate(0, 0);
    box-shadow: var(--shadow-inset);
  }

  /* Secondary Button - Brutalist */
  .btn-secondary {
    background: var(--color-secondary);
    color: var(--bg-primary);
    border-color: var(--color-secondary);
    box-shadow: var(--shadow-brutal-secondary);
  }

  .btn-secondary:hover:not(:disabled) {
    transform: translate(-2px, -2px);
    box-shadow: var(--shadow-brutal-lg);
  }

  /* Outline Button - Brutalist */
  .btn-outline {
    background: transparent;
    color: var(--text-primary);
    border-color: var(--text-primary);
    box-shadow: var(--shadow-brutal);
  }

  .btn-outline:hover:not(:disabled) {
    background: var(--text-primary);
    color: var(--bg-primary);
    transform: translate(-2px, -2px);
    box-shadow: var(--shadow-brutal-lg);
  }

  /* Button Sizes - 2025 Touch Standards */
  .btn-sm {
    padding: var(--space-3) var(--space-6);
    min-height: var(--space-10);  /* 40px minimum */
    font-size: var(--font-size-sm);
  }

  .btn-lg {
    padding: var(--space-5) var(--space-14);
    min-height: var(--space-12);  /* 48px minimum */
    font-size: var(--font-size-lg);
    font-weight: 700;
    gap: var(--space-4);
  }

  /* Brutalist Card System */
  .card {
    background: var(--bg-card);
    border: var(--border-width) solid var(--border-color);
    box-shadow: var(--shadow-brutal);
    transition: var(--transition-brutal);
    overflow: hidden;
    position: relative;
  }

  .card:hover {
    transform: translate(-4px, -4px);
    box-shadow: var(--shadow-brutal-xl);
  }

  .card-padding {
    padding: var(--space-6);
  }

  @media (min-width: 640px) {
    .card-padding {
      padding: var(--space-8);
    }
  }

  @media (min-width: 1024px) {
    .card-padding {
      padding: var(--space-10);
    }
  }

  /* Brutalist Glass Effect */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: var(--border-width) solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-brutal);
  }

  /* Brutalist Gradient Text */
  .gradient-text {
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 50%, var(--color-accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    font-weight: 700;
  }

  /* Brutalist Grid System - 2025 Spacing Standards */
  .grid-brutal {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
  }

  .grid-asymmetric {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-8);
    align-items: start;
  }

  @media (min-width: 640px) {
    .grid-brutal {
      gap: var(--space-10);
    }

    .grid-asymmetric {
      gap: var(--space-10);
    }
  }

  @media (min-width: 1024px) {
    .grid-brutal {
      gap: var(--space-12);
    }

    .grid-asymmetric {
      gap: var(--space-12);
    }
  }

  @media (max-width: 639px) {
    .grid-asymmetric {
      grid-template-columns: 1fr;
      gap: var(--space-6);
    }

    .grid-brutal {
      grid-template-columns: 1fr;
      gap: var(--space-6);
    }
  }
}

/* Modern Accessibility & Utility Styles - 2025 Standards */
@layer utilities {
  /* Screen reader only content - WCAG 2.2 compliant */
  .sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }

  /* Modern focus management */
  .keyboard-user *:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    border-radius: var(--border-radius-sm);
  }

  /* High contrast mode support */
  .high-contrast {
    filter: contrast(150%) brightness(110%);
  }

  /* Respect user motion preferences - 2025 Standard */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Modern focus indicators */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    border-radius: var(--border-radius-sm);
  }

  /* Accessible skip link */
  .skip-link {
    position: absolute;
    top: -100px;
    left: var(--space-4);
    background: var(--color-primary);
    color: white;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--border-radius);
    z-index: var(--z-modal);
    transition: var(--transition-base);
    font-weight: 600;
    text-decoration: none;
  }

  .skip-link:focus {
    top: var(--space-4);
  }

  /* Modern Animation System - 2025 */
  .fade-in {
    animation: fadeIn 600ms var(--transition-base);
  }

  .slide-up {
    animation: slideUp 600ms var(--transition-base);
  }

  .slide-down {
    animation: slideDown 600ms var(--transition-base);
  }

  .scale-in {
    animation: scaleIn 400ms var(--transition-bounce);
  }

  .rotate-in {
    animation: rotateIn 500ms var(--transition-base);
  }

  /* Modern Keyframes */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes rotateIn {
    from {
      opacity: 0;
      transform: rotate(-5deg) scale(0.95);
    }
    to {
      opacity: 1;
      transform: rotate(0deg) scale(1);
    }
  }

  /* Modern Responsive Typography - 2025 */
  .text-responsive {
    font-size: clamp(var(--font-size-base), 2.5vw, var(--font-size-lg));
    line-height: var(--leading-relaxed);
  }

  .heading-responsive {
    font-size: clamp(var(--font-size-2xl), 4vw, var(--font-size-4xl));
    line-height: var(--leading-tight);
  }

  .hero-text {
    font-size: clamp(var(--font-size-4xl), 6vw, var(--font-size-7xl));
    line-height: var(--leading-tight);
    font-weight: 800;
    letter-spacing: -0.05em;
  }

  .subheading-text {
    font-size: clamp(var(--font-size-lg), 3vw, var(--font-size-2xl));
    line-height: var(--leading-relaxed);
  }

  /* Modern Layout Utilities */
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .grid-center {
    display: grid;
    place-items: center;
  }

  /* 2025 Spacing Utilities - Optimal Readability */
  .space-y-fluid > * + * {
    margin-top: clamp(var(--space-6), 4vw, var(--space-12));
  }

  .space-x-fluid > * + * {
    margin-left: clamp(var(--space-6), 4vw, var(--space-12));
  }

  /* Optimal Reading Width - 60-80 characters */
  .reading-width {
    max-width: 65ch;
  }

  .reading-width-wide {
    max-width: 80ch;
  }

  /* Improved Vertical Rhythm */
  .vertical-rhythm > * + * {
    margin-top: var(--space-6);
  }

  .vertical-rhythm-lg > * + * {
    margin-top: var(--space-8);
  }

  /* Modern Aspect Ratios */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-photo {
    aspect-ratio: 4 / 3;
  }

  /* Modern Truncation */
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Brutalist Utility Classes */
  .text-brutal {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .border-brutal-primary {
    border: 4px solid var(--color-primary);
  }

  .border-brutal-secondary {
    border: 4px solid var(--color-secondary);
  }

  .border-brutal-accent {
    border: 4px solid var(--color-accent);
  }

  /* 2025 Typography Improvements */
  .text-responsive-xl {
    font-size: clamp(1.5rem, 4vw, 3rem);
    line-height: var(--leading-tight);
  }

  .text-responsive-lg {
    font-size: clamp(1.25rem, 3vw, 2rem);
    line-height: var(--leading-snug);
  }

  .text-responsive-base {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    line-height: var(--leading-normal);
  }

  /* Improved Focus States for 2025 */
  .focus-brutal:focus-visible {
    outline: 4px solid var(--color-primary);
    outline-offset: 4px;
    box-shadow: 0 0 0 8px rgba(var(--color-primary-rgb), 0.2);
  }
}
