import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Phone, Mail, MapPin, Send, MessageCircle, Clock, CheckCircle } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

const Contact = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const services = [
    'Web Development',
    'Social Media Management',
    'Brand Development',
    'Mobile App Development',
    'SEO Optimization',
    'Digital Marketing',
    'Other'
  ];

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    const content = contentRef.current;

    if (!section || !title || !content) return;

    // Animate title
    gsap.fromTo(title.children,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        stagger: 0.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: title,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Animate content
    gsap.fromTo(content.children,
      { opacity: 0, y: 80 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.15,
        ease: "power3.out",
        scrollTrigger: {
          trigger: content,
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Cleanup
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({
        name: '',
        email: '',
        phone: '',
        service: '',
        message: ''
      });
    }, 3000);
  };

  const handleWhatsAppClick = () => {
    const message = encodeURIComponent(
      `Hi! I'm interested in your services. ${formData.name ? `My name is ${formData.name}.` : ''} ${formData.service ? `I'm looking for ${formData.service} services.` : ''}`
    );
    window.open(`https://wa.me/94715768552?text=${message}`, '_blank');
  };

  return (
    <section ref={sectionRef} id="contact" className="section-padding" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      <div className="container-custom">
        <div ref={titleRef} className="space-y-8 md:space-y-12 lg:space-y-16 mb-16 md:mb-20 lg:mb-24">
          <div className="inline-block p-4 border-4 border-primary" style={{ borderColor: 'var(--color-primary)', backgroundColor: 'var(--color-primary)' }}>
            <span className="text-black font-bold text-lg mono uppercase tracking-wide">
              CONTACT
            </span>
          </div>
          <h2 className="text-6xl md:text-8xl lg:text-9xl font-bold leading-none" style={{ fontFamily: 'Space Grotesk, sans-serif', color: 'var(--text-primary)', lineHeight: 'var(--leading-none)' }}>
            LET'S
            <br />
            <span className="gradient-text">TALK</span>
          </h2>
          <div className="max-w-4xl">
            <p className="text-xl md:text-2xl lg:text-3xl font-medium" style={{ color: 'var(--text-secondary)', lineHeight: 'var(--leading-normal)' }}>
              READY TO STOP WASTING MONEY ON BAD WEBSITES?
              <br />
              LET'S BUILD SOMETHING THAT ACTUALLY CONVERTS.
            </p>
          </div>
        </div>

        <div ref={contentRef} className="grid-asymmetric gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div className="card card-padding">
              <h3 className="text-3xl font-bold mb-8 uppercase" style={{ fontFamily: 'Space Grotesk, sans-serif', color: 'var(--text-primary)' }}>
                GET IN TOUCH
              </h3>

              <div className="space-y-8 md:space-y-10 lg:space-y-12">
                <div className="flex items-start space-x-6 md:space-x-8 group">
                  <div className="w-16 h-16 md:w-20 md:h-20 border-4 border-primary flex items-center justify-center group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200" style={{ borderColor: 'var(--color-primary)', backgroundColor: 'var(--color-primary)' }}>
                    <Phone className="text-black" size={24} />
                  </div>
                  <div className="space-y-2 md:space-y-3">
                    <p className="font-bold text-base md:text-lg mono uppercase tracking-wide" style={{ color: 'var(--text-primary)' }}>PHONE</p>
                    <a
                      href="tel:+94715768552"
                      className="text-xl md:text-2xl font-bold mono hover:translate-x-1 transition-transform duration-200 inline-block"
                      style={{ color: 'var(--text-primary)' }}
                    >
                      +94 71 576 8552
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-6 md:space-x-8 group">
                  <div className="w-16 h-16 md:w-20 md:h-20 border-4 border-secondary flex items-center justify-center group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200" style={{ borderColor: 'var(--color-secondary)', backgroundColor: 'var(--color-secondary)' }}>
                    <Mail className="text-black" size={24} />
                  </div>
                  <div className="space-y-2 md:space-y-3">
                    <p className="font-bold text-base md:text-lg mono uppercase tracking-wide" style={{ color: 'var(--text-primary)' }}>EMAIL</p>
                    <a
                      href="mailto:<EMAIL>"
                      className="text-xl md:text-2xl font-bold mono hover:translate-x-1 transition-transform duration-200 inline-block"
                      style={{ color: 'var(--text-primary)' }}
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-6 md:space-x-8 group">
                  <div className="w-16 h-16 md:w-20 md:h-20 border-4 border-accent flex items-center justify-center group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200" style={{ borderColor: 'var(--color-accent)', backgroundColor: 'var(--color-accent)' }}>
                    <MapPin className="text-black" size={24} />
                  </div>
                  <div className="space-y-2 md:space-y-3">
                    <p className="font-bold text-base md:text-lg mono uppercase tracking-wide" style={{ color: 'var(--text-primary)' }}>LOCATION</p>
                    <p className="text-xl md:text-2xl font-bold mono" style={{ color: 'var(--text-primary)' }}>COLOMBO, SRI LANKA</p>
                  </div>
                </div>

                <div className="flex items-center space-x-6 group">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Clock className="text-yellow-300" size={24} />
                  </div>
                  <div>
                    <p className="font-semibold text-white mb-1">Business Hours</p>
                    <p className="text-white/80 text-lg">Mon - Fri: 9:00 AM - 6:00 PM</p>
                  </div>
                </div>
              </div>

              <div className="mt-10 pt-8 border-t border-white/20">
                <button
                  onClick={handleWhatsAppClick}
                  className="w-full btn btn-lg bg-green-600 hover:bg-green-700 text-white border-none"
                  aria-label="Contact us on WhatsApp - Contact Section"
                >
                  <MessageCircle size={20} />
                  Chat on WhatsApp
                </button>
              </div>
            </div>

            {/* Quick Response Promise */}
            <div className="glass rounded-3xl card-padding">
              <h4 className="text-xl font-semibold mb-4 text-white" style={{ fontFamily: 'Poppins, sans-serif' }}>Quick Response Guarantee</h4>
              <p className="text-white/90 mb-6 text-lg leading-relaxed">
                We respond to all inquiries within 2 hours during business hours.
              </p>
              <div className="flex items-center text-white/90">
                <CheckCircle size={20} className="mr-3 text-green-400" />
                <span className="font-medium">Average response time: 30 minutes</span>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="glass rounded-3xl card-padding">
            <h3 className="text-2xl font-bold mb-8 text-white" style={{ fontFamily: 'Poppins, sans-serif' }}>Send us a Message</h3>
            
            {isSubmitted ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="text-green-600" size={32} />
                </div>
                <h4 className="text-xl font-semibold text-green-600 mb-2">Message Sent!</h4>
                <p className="text-gray-600">
                  Thank you for reaching out. We'll get back to you within 2 hours.
                </p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-white mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-white/20 bg-white/10 backdrop-blur-sm rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-colors text-white placeholder-white/60"
                      placeholder="Your full name"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-white/20 bg-white/10 backdrop-blur-sm rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-colors text-white placeholder-white/60"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                      placeholder="+94 XX XXX XXXX"
                    />
                  </div>

                  <div>
                    <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                      Service Interested In
                    </label>
                    <select
                      id="service"
                      name="service"
                      value={formData.service}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                    >
                      <option value="">Select a service</option>
                      {services.map((service) => (
                        <option key={service} value={service}>
                          {service}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={5}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors resize-none"
                    placeholder="Tell us about your project..."
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full btn btn-primary"
                >
                  <Send size={20} className="mr-2" />
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </form>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
