import{S as m,j as e}from"./index-D-MEtesh.js";import{r as s,C as b,f as u,g as h,h as f,i as v,j as y}from"./ui-DOs47f7g.js";import{g as l}from"./gsap-CH_iu5NA.js";import"./vendor-1zw1pNgy.js";l.registerPlugin(m);const E=()=>{const i=s.useRef(null),c=s.useRef(null),d=s.useRef(null),g=[{icon:b,title:"Web Development",description:"Custom websites and web applications built with modern technologies, responsive design, and optimal performance.",features:["React & Next.js","E-commerce Solutions","Progressive Web Apps","API Integration"],color:"from-blue-500 to-cyan-500"},{icon:u,title:"Social Media Management",description:"Strategic social media campaigns that build communities, increase engagement, and drive meaningful conversions.",features:["Content Strategy","Community Management","Paid Advertising","Analytics & Reporting"],color:"from-purple-500 to-pink-500"},{icon:h,title:"Brand Development",description:"Complete brand identity solutions that resonate with your target audience and differentiate your business.",features:["Logo Design","Brand Guidelines","Visual Identity","Brand Strategy"],color:"from-orange-500 to-red-500"},{icon:f,title:"Mobile App Development",description:"Native and cross-platform mobile applications that deliver exceptional user experiences across all devices.",features:["iOS & Android","React Native","UI/UX Design","App Store Optimization"],color:"from-green-500 to-emerald-500"},{icon:v,title:"SEO Optimization",description:"Comprehensive SEO strategies that improve your search rankings and drive organic traffic to your website.",features:["Technical SEO","Content Optimization","Local SEO","Performance Monitoring"],color:"from-indigo-500 to-blue-500"},{icon:y,title:"Digital Marketing",description:"Data-driven marketing campaigns that maximize ROI and accelerate your business growth in the digital landscape.",features:["PPC Advertising","Email Marketing","Conversion Optimization","Marketing Automation"],color:"from-teal-500 to-cyan-500"}];return s.useEffect(()=>{const r=i.current,t=c.current,a=d.current;if(!(!r||!t||!a))return l.fromTo(t.children,{opacity:0,y:50},{opacity:1,y:0,duration:1,stagger:.2,ease:"power3.out",scrollTrigger:{trigger:t,start:"top 80%",end:"bottom 20%",toggleActions:"play none none reverse"}}),l.fromTo(a.children,{opacity:0,y:80,scale:.9},{opacity:1,y:0,scale:1,duration:.8,stagger:.15,ease:"power3.out",scrollTrigger:{trigger:a,start:"top 85%",end:"bottom 15%",toggleActions:"play none none reverse"}}),()=>{m.getAll().forEach(o=>o.kill())}},[]),e.jsx("section",{ref:i,id:"services",className:"section-padding",style:{backgroundColor:"var(--bg-secondary)"},children:e.jsxs("div",{className:"container-custom",children:[e.jsxs("div",{ref:c,className:"space-y-8 md:space-y-12 lg:space-y-16 mb-16 md:mb-20 lg:mb-24",children:[e.jsx("div",{className:"inline-block p-4 border-4 border-secondary",style:{borderColor:"var(--color-secondary)",backgroundColor:"var(--color-secondary)"},children:e.jsx("span",{className:"text-black font-bold text-lg mono uppercase tracking-wide",children:"SERVICES"})}),e.jsxs("h2",{className:"text-6xl md:text-8xl lg:text-9xl font-bold leading-none",style:{fontFamily:"Space Grotesk, sans-serif",color:"var(--text-primary)",lineHeight:"var(--leading-none)"},children:["WHAT WE",e.jsx("br",{}),e.jsx("span",{className:"gradient-text",children:"BUILD"})]}),e.jsx("div",{className:"max-w-4xl",children:e.jsxs("p",{className:"text-xl md:text-2xl lg:text-3xl font-medium",style:{color:"var(--text-secondary)",lineHeight:"var(--leading-normal)"},children:["WE DON'T DO EVERYTHING. WE DO THESE THINGS REALLY WELL.",e.jsx("br",{}),"NO FLUFF. NO BS. JUST RESULTS."]})})]}),e.jsx("div",{ref:d,className:"grid-brutal",children:g.map((r,t)=>{const a=r.icon,o=[{bg:"var(--color-primary)",border:"var(--color-primary)"},{bg:"var(--color-secondary)",border:"var(--color-secondary)"},{bg:"var(--color-accent)",border:"var(--color-accent)"},{bg:"var(--color-primary)",border:"var(--color-primary)"},{bg:"var(--color-secondary)",border:"var(--color-secondary)"},{bg:"var(--color-accent)",border:"var(--color-accent)"}],n=o[t%o.length];return e.jsxs("div",{className:"card card-padding group h-full flex flex-col",children:[e.jsxs("div",{className:"flex items-start gap-6 mb-6 md:mb-8",children:[e.jsx("div",{className:"w-16 h-16 md:w-20 md:h-20 border-4 flex items-center justify-center group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200",style:{borderColor:n.border,backgroundColor:n.bg},children:e.jsx(a,{size:28,className:"text-black md:w-8 md:h-8"})}),e.jsx("div",{className:"flex-1",children:e.jsx("h3",{className:"text-2xl md:text-3xl lg:text-4xl font-bold uppercase",style:{fontFamily:"Space Grotesk, sans-serif",color:"var(--text-primary)",lineHeight:"var(--leading-tight)"},children:r.title})})]}),e.jsx("p",{className:"mb-6 md:mb-8 text-lg md:text-xl font-medium flex-grow",style:{color:"var(--text-secondary)",lineHeight:"var(--leading-normal)"},children:r.description}),e.jsx("ul",{className:"text-sm md:text-base mb-8 md:mb-10 space-y-3",style:{color:"var(--text-muted)"},children:r.features.map((p,x)=>e.jsxs("li",{className:"flex items-start gap-3 md:gap-4",children:[e.jsx("span",{className:"w-2 h-2 md:w-3 md:h-3 mt-2 flex-shrink-0",style:{backgroundColor:n.bg}}),e.jsx("span",{className:"mono uppercase tracking-wide font-medium",children:p})]},x))}),e.jsx("button",{className:"btn btn-outline w-full mt-auto",children:"LEARN MORE"})]},t)})}),e.jsx("div",{className:"mt-20",children:e.jsx("div",{className:"card card-padding",style:{backgroundColor:"var(--color-primary)",borderColor:"var(--color-primary)"},children:e.jsxs("div",{className:"grid-asymmetric items-center",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-4xl md:text-5xl font-bold mb-6 text-black uppercase",style:{fontFamily:"Space Grotesk, sans-serif"},children:["READY TO",e.jsx("br",{}),"START?"]}),e.jsxs("p",{className:"text-lg text-black/80 mb-8 leading-tight",children:["STOP WASTING TIME WITH AGENCIES THAT DON'T GET IT.",e.jsx("br",{}),"LET'S BUILD SOMETHING THAT ACTUALLY WORKS."]})]}),e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("button",{className:"btn btn-lg text-black border-black hover:bg-black hover:text-primary",style:{backgroundColor:"transparent",borderColor:"black",color:"black"},onClick:()=>window.open("https://wa.me/94715768552","_blank"),children:"START PROJECT"}),e.jsx("button",{className:"btn btn-lg text-black border-black hover:bg-black hover:text-primary",style:{backgroundColor:"transparent",borderColor:"black",color:"black"},children:"VIEW PROCESS"})]})]})})})]})})};export{E as default};
