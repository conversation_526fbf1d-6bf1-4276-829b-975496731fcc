@import"https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap";/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-border-style:solid;--tw-leading:initial;--tw-outline-style:solid;--tw-duration:initial;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000}}}.pointer-events-none{pointer-events:none}.visible{visibility:visible}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.top-1\/2{top:50%}.top-1\/4{top:25%}.top-2\/3{top:66.6667%}.right-1\/4{right:25%}.bottom-1\/3{bottom:33.3333%}.left-1\/3{left:33.3333%}.left-1\/4{left:25%}.z-10{z-index:10}.z-50{z-index:50}.float-left{float:left}.container{width:100%}.mx-auto{margin-inline:auto}.mt-auto{margin-top:auto}.line-clamp-3{-webkit-line-clamp:3;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline-block{display:inline-block}.inline-flex{display:inline-flex}.h-full{height:100%}.max-h-\[90vh\]{max-height:90vh}.min-h-screen{min-height:100vh}.w-full{width:100%}.flex-1{flex:1}.flex-shrink-0{flex-shrink:0}.flex-grow{flex-grow:1}.-rotate-12{rotate:-12deg}.rotate-12{rotate:12deg}.rotate-45{rotate:45deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.cursor-default{cursor:default}.cursor-pointer{cursor:pointer}.resize{resize:both}.resize-none{resize:none}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-start{align-items:flex-start}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.overflow-hidden{overflow:hidden}.overflow-y-auto{overflow-y:auto}.rounded-full{border-radius:3.40282e38px}.border{border-style:var(--tw-border-style);border-width:1px}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-4{border-style:var(--tw-border-style);border-width:4px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-b-4{border-bottom-style:var(--tw-border-style);border-bottom-width:4px}.border-none{--tw-border-style:none;border-style:none}.object-cover{-o-object-fit:cover;object-fit:cover}.text-center{text-align:center}.text-left{text-align:left}.leading-none{--tw-leading:1;line-height:1}.uppercase{text-transform:uppercase}.italic{font-style:italic}.line-through{text-decoration-line:line-through}.overline{text-decoration-line:overline}.underline{text-decoration-line:underline}.opacity-0{opacity:0}.opacity-5{opacity:.05}.opacity-25{opacity:.25}.opacity-75{opacity:.75}.opacity-100{opacity:1}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition\!{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events!important;transition-timing-function:var(--tw-ease,ease)!important;transition-duration:var(--tw-duration,0s)!important}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}@media (hover:hover){.group-hover\:w-full:is(:where(.group):hover *){width:100%}.group-hover\:scale-105:is(:where(.group):hover *){--tw-scale-x:105%;--tw-scale-y:105%;--tw-scale-z:105%;scale:var(--tw-scale-x)var(--tw-scale-y)}.group-hover\:scale-110:is(:where(.group):hover *){--tw-scale-x:110%;--tw-scale-y:110%;--tw-scale-z:110%;scale:var(--tw-scale-x)var(--tw-scale-y)}.group-hover\:opacity-100:is(:where(.group):hover *){opacity:1}.hover\:scale-110:hover{--tw-scale-x:110%;--tw-scale-y:110%;--tw-scale-z:110%;scale:var(--tw-scale-x)var(--tw-scale-y)}}.focus\:border-transparent:focus{border-color:#0000}.focus\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\:outline-none:focus{--tw-outline-style:none;outline-style:none}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:opacity-50:disabled{opacity:.5}:root{--color-primary:#0f8;--color-primary-dark:#00cc6a;--color-primary-light:#3f9;--color-secondary:#f06;--color-accent:#ff0;--color-accent-dark:#cc0;--color-warning:#f60;--color-error:#f03;--color-info:#09f;--bg-primary:#0a0a0a;--bg-secondary:#111;--bg-tertiary:#1a1a1a;--bg-card:#151515;--bg-glass:#ffffff0d;--bg-overlay:#000c;--bg-noise:url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.02'/%3E%3C/svg%3E");--text-primary:#fff;--text-secondary:#ccc;--text-muted:#888;--text-inverse:#000;--text-accent:var(--color-primary);--border-color:#333;--border-color-light:#444;--border-color-dark:#222;--border-radius-none:0;--border-radius-sm:2px;--border-radius:4px;--border-radius-lg:8px;--border-width:2px;--border-width-thick:4px;--shadow-brutal:4px 4px 0px var(--color-primary);--shadow-brutal-lg:8px 8px 0px var(--color-primary);--shadow-brutal-xl:12px 12px 0px var(--color-primary);--shadow-brutal-secondary:4px 4px 0px var(--color-secondary);--shadow-brutal-accent:4px 4px 0px var(--color-accent);--shadow-inset:inset 2px 2px 4px #0000004d;--space-1:.25rem;--space-2:.5rem;--space-3:.75rem;--space-4:1rem;--space-5:1.25rem;--space-6:1.5rem;--space-7:1.75rem;--space-8:2rem;--space-9:2.25rem;--space-10:2.5rem;--space-11:2.75rem;--space-12:3rem;--space-14:3.5rem;--space-16:4rem;--space-18:4.5rem;--space-20:5rem;--space-24:6rem;--space-28:7rem;--space-32:8rem;--space-40:10rem;--space-48:12rem;--space-64:16rem;--font-size-xs:.75rem;--font-size-sm:.875rem;--font-size-base:1rem;--font-size-lg:1.125rem;--font-size-xl:1.25rem;--font-size-2xl:1.5rem;--font-size-3xl:2rem;--font-size-4xl:2.5rem;--font-size-5xl:3rem;--font-size-6xl:4rem;--font-size-7xl:5rem;--font-size-8xl:6rem;--font-size-9xl:8rem;--leading-none:1;--leading-tight:1.1;--leading-snug:1.2;--leading-normal:1.5;--leading-relaxed:1.6;--leading-loose:1.8;--transition-instant:.1s ease-out;--transition-fast:.2s ease-out;--transition-base:.3s ease-out;--transition-slow:.5s ease-out;--transition-brutal:.15s cubic-bezier(.25,.46,.45,.94);--z-base:1;--z-dropdown:1000;--z-sticky:1020;--z-fixed:1030;--z-modal-backdrop:1040;--z-modal:1050;--z-tooltip:1070}@layer base{*,:before,:after{box-sizing:border-box;margin:0;padding:0}html{scroll-behavior:smooth;font-size:16px;line-height:var(--leading-normal);-webkit-text-size-adjust:100%;-moz-text-size-adjust:100%;text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4}body{background:var(--bg-primary);background-image:var(--bg-noise);color:var(--text-primary);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;min-height:100vh;font-family:Space Grotesk,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;font-weight:400;font-size:var(--font-size-base);line-height:var(--leading-normal);position:relative;overflow-x:hidden}body:before{content:"";background:var(--bg-noise);opacity:.03;pointer-events:none;z-index:1;width:100%;height:100%;position:fixed;top:0;left:0}::-webkit-scrollbar{width:12px;height:12px}::-webkit-scrollbar-track{background:var(--bg-secondary);border:var(--border-width)solid var(--border-color)}::-webkit-scrollbar-thumb{background:var(--color-primary);border:var(--border-width)solid var(--bg-primary);box-shadow:var(--shadow-brutal)}::-webkit-scrollbar-thumb:hover{background:var(--color-primary-light);box-shadow:var(--shadow-brutal-lg)}::-webkit-scrollbar-corner{background:var(--bg-secondary)}h1,h2,h3,h4,h5,h6{font-family:Space Grotesk,sans-serif;font-weight:700;line-height:var(--leading-tight);color:var(--text-primary);letter-spacing:-.02em;text-transform:uppercase}h1{font-size:clamp(var(--font-size-5xl),8vw,var(--font-size-9xl));letter-spacing:-.04em;font-weight:700;line-height:var(--leading-none)}h2{font-size:clamp(var(--font-size-4xl),6vw,var(--font-size-7xl));font-weight:700;line-height:var(--leading-tight)}h3{font-size:clamp(var(--font-size-3xl),4vw,var(--font-size-5xl));font-weight:600}h4{font-size:clamp(var(--font-size-2xl),3vw,var(--font-size-4xl));font-weight:600}h5{font-size:var(--font-size-xl);font-weight:500}h6{font-size:var(--font-size-lg);font-weight:500}p{line-height:var(--leading-normal);color:var(--text-secondary);font-size:var(--font-size-base);font-weight:400}.mono{font-family:JetBrains Mono,monospace;font-weight:400}a{color:var(--color-primary);transition:var(--transition-fast);outline:none;text-decoration:none}a:hover{color:var(--color-primary-dark)}a:focus-visible{outline:2px solid var(--color-primary);outline-offset:2px;border-radius:var(--border-radius-sm)}input,textarea,select,button{font-family:inherit;font-size:inherit;line-height:inherit}button{cursor:pointer;background:0 0;border:none;outline:none}button:disabled{cursor:not-allowed;opacity:.6}img,picture,video,canvas,svg{max-width:100%;height:auto;display:block}ul,ol{list-style:none}table{border-collapse:collapse;border-spacing:0}}@layer components{.container-custom{max-width:1400px;padding:0 var(--space-4);width:100%;z-index:var(--z-base);margin:0 auto;position:relative}@media (min-width:640px){.container-custom{padding:0 var(--space-6)}}@media (min-width:1024px){.container-custom{padding:0 var(--space-8)}}@media (min-width:1280px){.container-custom{padding:0 var(--space-12)}}.section-padding{padding:var(--space-16)0}@media (min-width:640px){.section-padding{padding:var(--space-20)0}}@media (min-width:768px){.section-padding{padding:var(--space-24)0}}@media (min-width:1024px){.section-padding{padding:var(--space-32)0}}@media (min-width:1280px){.section-padding{padding:var(--space-40)0}}.btn{justify-content:center;align-items:center;gap:var(--space-3);padding:var(--space-4)var(--space-8);min-height:var(--space-11);min-width:var(--space-11);border:var(--border-width)solid var(--border-color);font-family:Space Grotesk,sans-serif;font-weight:600;font-size:var(--font-size-base);line-height:var(--leading-normal);text-transform:uppercase;letter-spacing:.05em;transition:var(--transition-brutal);cursor:pointer;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;user-select:none;background:var(--bg-card);color:var(--text-primary);outline:none;text-decoration:none;display:inline-flex;position:relative}.btn:focus-visible{outline:var(--border-width)solid var(--color-primary);outline-offset:2px}.btn:disabled{opacity:.5;cursor:not-allowed;box-shadow:none!important;transform:none!important}.btn-primary{background:var(--color-primary);color:var(--bg-primary);border-color:var(--color-primary);box-shadow:var(--shadow-brutal)}.btn-primary:hover:not(:disabled){box-shadow:var(--shadow-brutal-lg);transform:translate(-2px,-2px)}.btn-primary:active{box-shadow:var(--shadow-inset);transform:translate(0)}.btn-secondary{background:var(--color-secondary);color:var(--bg-primary);border-color:var(--color-secondary);box-shadow:var(--shadow-brutal-secondary)}.btn-secondary:hover:not(:disabled){box-shadow:var(--shadow-brutal-lg);transform:translate(-2px,-2px)}.btn-outline{color:var(--text-primary);border-color:var(--text-primary);box-shadow:var(--shadow-brutal);background:0 0}.btn-outline:hover:not(:disabled){background:var(--text-primary);color:var(--bg-primary);box-shadow:var(--shadow-brutal-lg);transform:translate(-2px,-2px)}.btn-sm{padding:var(--space-3)var(--space-6);min-height:var(--space-10);font-size:var(--font-size-sm)}.btn-lg{padding:var(--space-5)var(--space-14);min-height:var(--space-12);font-size:var(--font-size-lg);gap:var(--space-4);font-weight:700}.card{background:var(--bg-card);border:var(--border-width)solid var(--border-color);box-shadow:var(--shadow-brutal);transition:var(--transition-brutal);position:relative;overflow:hidden}.card:hover{box-shadow:var(--shadow-brutal-xl);transform:translate(-4px,-4px)}.card-padding{padding:var(--space-6)}@media (min-width:640px){.card-padding{padding:var(--space-8)}}@media (min-width:1024px){.card-padding{padding:var(--space-10)}}.glass{-webkit-backdrop-filter:blur(10px);border:var(--border-width)solid #ffffff1a;box-shadow:var(--shadow-brutal);background:#ffffff0d}.gradient-text{background:linear-gradient(90deg,var(--color-primary)0%,var(--color-secondary)50%,var(--color-accent)100%);-webkit-text-fill-color:transparent;color:#0000;-webkit-background-clip:text;background-clip:text;font-weight:700}.grid-brutal{gap:var(--space-8);grid-template-columns:repeat(auto-fit,minmax(350px,1fr));display:grid}.grid-asymmetric{gap:var(--space-8);grid-template-columns:2fr 1fr;align-items:start;display:grid}@media (min-width:640px){.grid-brutal,.grid-asymmetric{gap:var(--space-10)}}@media (min-width:1024px){.grid-brutal,.grid-asymmetric{gap:var(--space-12)}}@media (max-width:639px){.grid-asymmetric,.grid-brutal{gap:var(--space-6);grid-template-columns:1fr}}}@layer utilities{.sr-only{clip:rect(0,0,0,0)!important;white-space:nowrap!important;border:0!important;width:1px!important;height:1px!important;margin:-1px!important;padding:0!important;position:absolute!important;overflow:hidden!important}.keyboard-user :focus-visible{outline:2px solid var(--color-primary);outline-offset:2px;border-radius:var(--border-radius-sm)}.high-contrast{filter:contrast(150%)brightness(110%)}@media (prefers-reduced-motion:reduce){*,:before,:after{scroll-behavior:auto!important;transition-duration:.01ms!important;animation-duration:.01ms!important;animation-iteration-count:1!important}}button:focus-visible,a:focus-visible,input:focus-visible,textarea:focus-visible,select:focus-visible{outline:2px solid var(--color-primary);outline-offset:2px;border-radius:var(--border-radius-sm)}.skip-link{top:-100px;left:var(--space-4);background:var(--color-primary);color:#fff;padding:var(--space-3)var(--space-4);border-radius:var(--border-radius);z-index:var(--z-modal);transition:var(--transition-base);font-weight:600;text-decoration:none;position:absolute}.skip-link:focus{top:var(--space-4)}.fade-in{animation:fadeIn .6s var(--transition-base)}.slide-up{animation:slideUp .6s var(--transition-base)}.slide-down{animation:slideDown .6s var(--transition-base)}.scale-in{animation:scaleIn .4s var(--transition-bounce)}.rotate-in{animation:rotateIn .5s var(--transition-base)}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes slideUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@keyframes slideDown{0%{opacity:0;transform:translateY(-30px)}to{opacity:1;transform:translateY(0)}}@keyframes scaleIn{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}@keyframes rotateIn{0%{opacity:0;transform:rotate(-5deg)scale(.95)}to{opacity:1;transform:rotate(0)scale(1)}}.text-responsive{font-size:clamp(var(--font-size-base),2.5vw,var(--font-size-lg));line-height:var(--leading-relaxed)}.heading-responsive{font-size:clamp(var(--font-size-2xl),4vw,var(--font-size-4xl));line-height:var(--leading-tight)}.hero-text{font-size:clamp(var(--font-size-4xl),6vw,var(--font-size-7xl));line-height:var(--leading-tight);letter-spacing:-.05em;font-weight:800}.subheading-text{font-size:clamp(var(--font-size-lg),3vw,var(--font-size-2xl));line-height:var(--leading-relaxed)}.flex-center{justify-content:center;align-items:center;display:flex}.flex-between{justify-content:space-between;align-items:center;display:flex}.grid-center{place-items:center;display:grid}.space-y-fluid>*+*{margin-top:clamp(var(--space-6),4vw,var(--space-12))}.space-x-fluid>*+*{margin-left:clamp(var(--space-6),4vw,var(--space-12))}.reading-width{max-width:65ch}.reading-width-wide{max-width:80ch}.vertical-rhythm>*+*{margin-top:var(--space-6)}.vertical-rhythm-lg>*+*{margin-top:var(--space-8)}.aspect-square{aspect-ratio:1}.aspect-video{aspect-ratio:16/9}.aspect-photo{aspect-ratio:4/3}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.line-clamp-2{-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.line-clamp-3{-webkit-line-clamp:3;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.text-brutal{text-transform:uppercase;letter-spacing:.05em;font-family:Space Grotesk,sans-serif;font-weight:700}.border-brutal-primary{border:4px solid var(--color-primary)}.border-brutal-secondary{border:4px solid var(--color-secondary)}.border-brutal-accent{border:4px solid var(--color-accent)}.text-responsive-xl{font-size:clamp(1.5rem,4vw,3rem);line-height:var(--leading-tight)}.text-responsive-lg{font-size:clamp(1.25rem,3vw,2rem);line-height:var(--leading-snug)}.text-responsive-base{font-size:clamp(1rem,2.5vw,1.25rem);line-height:var(--leading-normal)}.focus-brutal:focus-visible{outline:4px solid var(--color-primary);outline-offset:4px;box-shadow:0 0 0 8px rgba(var(--color-primary-rgb),.2)}}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-leading{syntax:"*";inherits:false}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-duration{syntax:"*";inherits:false}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}
