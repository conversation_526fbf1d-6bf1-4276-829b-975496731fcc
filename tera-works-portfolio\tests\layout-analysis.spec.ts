import { test, expect } from '@playwright/test';

test.describe('Layout Analysis - Before Spacing Improvements', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:4173');
    await page.waitForLoadState('networkidle');
  });

  test('Capture current layout - Desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Full page screenshot
    await page.screenshot({ 
      path: 'tests/screenshots/before/desktop-full-page.png',
      fullPage: true 
    });

    // Individual sections
    await page.locator('#home').screenshot({ 
      path: 'tests/screenshots/before/desktop-hero.png' 
    });
    
    await page.locator('#services').screenshot({ 
      path: 'tests/screenshots/before/desktop-services.png' 
    });
    
    await page.locator('#portfolio').screenshot({ 
      path: 'tests/screenshots/before/desktop-portfolio.png' 
    });
    
    await page.locator('#contact').screenshot({ 
      path: 'tests/screenshots/before/desktop-contact.png' 
    });
    
    await page.locator('footer').screenshot({ 
      path: 'tests/screenshots/before/desktop-footer.png' 
    });
  });

  test('Capture current layout - Tablet', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    
    await page.screenshot({ 
      path: 'tests/screenshots/before/tablet-full-page.png',
      fullPage: true 
    });

    await page.locator('#home').screenshot({ 
      path: 'tests/screenshots/before/tablet-hero.png' 
    });
    
    await page.locator('#services').screenshot({ 
      path: 'tests/screenshots/before/tablet-services.png' 
    });
    
    await page.locator('#portfolio').screenshot({ 
      path: 'tests/screenshots/before/tablet-portfolio.png' 
    });
  });

  test('Capture current layout - Mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.screenshot({ 
      path: 'tests/screenshots/before/mobile-full-page.png',
      fullPage: true 
    });

    await page.locator('#home').screenshot({ 
      path: 'tests/screenshots/before/mobile-hero.png' 
    });
    
    await page.locator('#services').screenshot({ 
      path: 'tests/screenshots/before/mobile-services.png' 
    });
    
    await page.locator('#portfolio').screenshot({ 
      path: 'tests/screenshots/before/mobile-portfolio.png' 
    });
  });

  test('Analyze spacing measurements', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Measure section paddings
    const heroSection = page.locator('#home');
    const servicesSection = page.locator('#services');
    const portfolioSection = page.locator('#portfolio');
    
    const heroBox = await heroSection.boundingBox();
    const servicesBox = await servicesSection.boundingBox();
    const portfolioBox = await portfolioSection.boundingBox();
    
    console.log('Current section measurements:');
    console.log('Hero height:', heroBox?.height);
    console.log('Services height:', servicesBox?.height);
    console.log('Portfolio height:', portfolioBox?.height);
    
    // Check button sizes
    const buttons = page.locator('.btn');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 3); i++) {
      const button = buttons.nth(i);
      const box = await button.boundingBox();
      console.log(`Button ${i + 1} size:`, box?.width, 'x', box?.height);
    }
  });
});
