import { test, expect } from '@playwright/test';

test.describe('Verify Spacing Improvements Are Applied', () => {
  test.beforeEach(async ({ page }) => {
    // Force cache refresh
    await page.goto('http://localhost:4173?t=' + Date.now());
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
  });

  test('Verify CSS custom properties are loaded', async ({ page }) => {
    // Check if the new spacing variables are available
    const space11Value = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue('--space-11');
    });
    
    console.log('--space-11 value:', space11Value);
    expect(space11Value.trim()).toBe('2.75rem');
    
    const leadingNormal = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue('--leading-normal');
    });
    
    console.log('--leading-normal value:', leadingNormal);
    expect(leadingNormal.trim()).toBe('1.5');
  });

  test('Verify button minimum heights', async ({ page }) => {
    const buttons = page.locator('.btn');
    const firstButton = buttons.first();
    
    const minHeight = await firstButton.evaluate(el => {
      return getComputedStyle(el).minHeight;
    });
    
    console.log('Button min-height:', minHeight);
    expect(minHeight).toBe('44px'); // 2.75rem = 44px
  });

  test('Verify section padding improvements', async ({ page }) => {
    const heroSection = page.locator('#home');
    
    const paddingTop = await heroSection.evaluate(el => {
      return getComputedStyle(el).paddingTop;
    });
    
    console.log('Hero section padding-top:', paddingTop);
    // Should be at least 64px (--space-16) on mobile
    const paddingValue = parseInt(paddingTop);
    expect(paddingValue).toBeGreaterThanOrEqual(64);
  });

  test('Verify line height improvements', async ({ page }) => {
    const paragraph = page.locator('p').first();
    
    const lineHeight = await paragraph.evaluate(el => {
      return getComputedStyle(el).lineHeight;
    });
    
    console.log('Paragraph line-height:', lineHeight);
    // Should be 1.5 times the font size (24px for 16px font)
    expect(lineHeight).toBe('24px');
  });

  test('Take screenshot to verify visual changes', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Take a screenshot to verify the changes are visible
    await page.screenshot({ 
      path: 'tests/screenshots/spacing-verification.png',
      fullPage: true 
    });
    
    // Check if buttons are properly sized
    const buttonBox = await page.locator('.btn').first().boundingBox();
    if (buttonBox) {
      console.log(`Button dimensions: ${buttonBox.width}x${buttonBox.height}`);
      expect(buttonBox.height).toBeGreaterThanOrEqual(44);
    }
  });
});
