import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ArrowRight, Play, Star, Users, Award, Zap } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

const Hero = () => {
  const heroRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);
  const backgroundRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const hero = heroRef.current;
    const title = titleRef.current;
    const subtitle = subtitleRef.current;
    const buttons = buttonsRef.current;
    const stats = statsRef.current;
    const background = backgroundRef.current;

    if (!hero || !title || !subtitle || !buttons || !stats || !background) return;

    // Set initial states
    gsap.set([title, subtitle, buttons, stats], { opacity: 0, y: 50 });
    gsap.set(background.children, { scale: 0, opacity: 0 });

    // Create timeline for entrance animations
    const tl = gsap.timeline({ delay: 0.3 });

    // Animate background elements
    tl.to(background.children, {
      scale: 1,
      opacity: 1,
      duration: 2,
      stagger: 0.2,
      ease: "power2.out"
    }, 0);

    // Animate text elements with stagger
    tl.to(title, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: "power3.out"
    }, 0.2)
    .to(subtitle, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, 0.4)
    .to(buttons, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ease: "power3.out"
    }, 0.6)
    .to(stats, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ease: "power3.out"
    }, 0.8);

    // Parallax effect for background elements
    gsap.to(background.children, {
      y: -50,
      scrollTrigger: {
        trigger: hero,
        start: "top bottom",
        end: "bottom top",
        scrub: 1
      }
    });

    // Cleanup
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const handleViewWork = () => {
    const portfolioSection = document.querySelector('#portfolio');
    if (portfolioSection) {
      portfolioSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleLetsTalk = () => {
    window.open('https://wa.me/94715768552', '_blank');
  };

  return (
    <section
      ref={heroRef}
      id="home"
      className="min-h-screen flex items-center justify-center relative overflow-hidden section-padding"
      style={{
        background: 'var(--bg-primary)',
      }}
    >
      {/* Brutalist Background Elements */}
      <div className="absolute inset-0">
        {/* Large geometric shapes */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary border-4 border-primary transform rotate-12" style={{ backgroundColor: 'var(--color-primary)', borderColor: 'var(--color-primary)' }}></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-secondary border-4 border-secondary transform -rotate-12" style={{ backgroundColor: 'var(--color-secondary)', borderColor: 'var(--color-secondary)' }}></div>
        <div className="absolute top-1/2 right-20 w-16 h-16 bg-accent border-4 border-accent transform rotate-45" style={{ backgroundColor: 'var(--color-accent)', borderColor: 'var(--color-accent)' }}></div>

        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `linear-gradient(var(--border-color) 1px, transparent 1px), linear-gradient(90deg, var(--border-color) 1px, transparent 1px)`,
          backgroundSize: '40px 40px'
        }}></div>
      </div>

      <div className="container-custom z-10">
        <div className="max-w-7xl mx-auto space-y-12 md:space-y-16 lg:space-y-20">
          {/* Brutalist Brand Identity */}
          <div>
            <div className="inline-block p-6 border-4 border-primary" style={{ borderColor: 'var(--color-primary)', backgroundColor: 'var(--color-primary)' }}>
              <h1 className="text-3xl md:text-4xl font-bold text-black mono">
                TERA.WORKS
              </h1>
            </div>
          </div>

          {/* Brutalist Hero Heading */}
          <div>
            <h2
              ref={titleRef}
              className="text-7xl md:text-9xl lg:text-[12rem] font-bold leading-none mb-6 md:mb-8"
              style={{ fontFamily: 'Space Grotesk, sans-serif', color: 'var(--text-primary)', lineHeight: 'var(--leading-none)' }}
            >
              DIGITAL
              <br />
              <span className="gradient-text">AGENCY</span>
              <br />
              <span className="text-5xl md:text-7xl lg:text-8xl">FOR THE FUTURE</span>
            </h2>
          </div>

          {/* Brutalist Subtitle */}
          <div>
            <div className="max-w-4xl">
              <p
                ref={subtitleRef}
                className="text-xl md:text-2xl lg:text-3xl font-medium"
                style={{ color: 'var(--text-secondary)', lineHeight: 'var(--leading-normal)' }}
              >
                WE BUILD WEBSITES THAT DON'T SUCK.
                <br />
                MANAGE SOCIAL MEDIA THAT ACTUALLY WORKS.
                <br />
                CREATE BRANDS PEOPLE REMEMBER.
              </p>
            </div>
          </div>

          {/* Brutalist CTA Buttons */}
          <div
            ref={buttonsRef}
            className="flex flex-col sm:flex-row gap-6 md:gap-8"
          >
            <button
              onClick={handleViewWork}
              className="btn btn-primary btn-lg group"
            >
              <Play size={24} />
              VIEW WORK
              <ArrowRight size={24} className="group-hover:translate-x-1 transition-transform" />
            </button>
            <button
              onClick={handleLetsTalk}
              className="btn btn-outline btn-lg"
            >
              LET'S TALK
            </button>
          </div>

          {/* Brutalist Stats Grid */}
          <div
            ref={statsRef}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8 md:gap-10 lg:gap-12"
          >
            <div className="text-left space-y-4">
              <div className="flex items-center">
                <div className="w-16 h-16 md:w-20 md:h-20 border-4 border-primary flex items-center justify-center" style={{ borderColor: 'var(--color-primary)', backgroundColor: 'var(--color-primary)' }}>
                  <Award className="text-black" size={28} />
                </div>
              </div>
              <div className="text-4xl md:text-5xl lg:text-6xl font-bold mono" style={{ color: 'var(--text-primary)', lineHeight: 'var(--leading-tight)' }}>50+</div>
              <div className="text-sm md:text-base font-bold uppercase tracking-wide" style={{ color: 'var(--text-secondary)' }}>PROJECTS DONE</div>
            </div>
            <div className="text-left space-y-4">
              <div className="flex items-center">
                <div className="w-16 h-16 md:w-20 md:h-20 border-4 border-secondary flex items-center justify-center" style={{ borderColor: 'var(--color-secondary)', backgroundColor: 'var(--color-secondary)' }}>
                  <Star className="text-black" size={28} />
                </div>
              </div>
              <div className="text-4xl md:text-5xl lg:text-6xl font-bold mono" style={{ color: 'var(--text-primary)', lineHeight: 'var(--leading-tight)' }}>100%</div>
              <div className="text-sm md:text-base font-bold uppercase tracking-wide" style={{ color: 'var(--text-secondary)' }}>HAPPY CLIENTS</div>
            </div>
            <div className="text-left space-y-4">
              <div className="flex items-center">
                <div className="w-16 h-16 md:w-20 md:h-20 border-4 border-accent flex items-center justify-center" style={{ borderColor: 'var(--color-accent)', backgroundColor: 'var(--color-accent)' }}>
                  <Users className="text-black" size={28} />
                </div>
              </div>
              <div className="text-4xl md:text-5xl lg:text-6xl font-bold mono" style={{ color: 'var(--text-primary)', lineHeight: 'var(--leading-tight)' }}>24/7</div>
              <div className="text-sm md:text-base font-bold uppercase tracking-wide" style={{ color: 'var(--text-secondary)' }}>SUPPORT</div>
            </div>
            <div className="text-left space-y-4">
              <div className="flex items-center">
                <div className="w-16 h-16 md:w-20 md:h-20 border-4 border-primary flex items-center justify-center" style={{ borderColor: 'var(--color-primary)', backgroundColor: 'var(--color-primary)' }}>
                  <Zap className="text-black" size={28} />
                </div>
              </div>
              <div className="text-4xl md:text-5xl lg:text-6xl font-bold mono" style={{ color: 'var(--text-primary)', lineHeight: 'var(--leading-tight)' }}>FAST</div>
              <div className="text-sm md:text-base font-bold uppercase tracking-wide" style={{ color: 'var(--text-secondary)' }}>DELIVERY</div>
            </div>
          </div>
        </div>
      </div>

      {/* Brutalist Animated Background Elements */}
      <div
        ref={backgroundRef}
        className="absolute inset-0 overflow-hidden pointer-events-none"
      >
        <div className="absolute top-1/4 left-1/3 w-2 h-2 bg-primary animate-pulse" style={{ backgroundColor: 'var(--color-primary)' }}></div>
        <div className="absolute bottom-1/3 right-1/4 w-2 h-2 bg-secondary animate-pulse" style={{ backgroundColor: 'var(--color-secondary)' }}></div>
        <div className="absolute top-2/3 left-1/4 w-2 h-2 bg-accent animate-pulse" style={{ backgroundColor: 'var(--color-accent)' }}></div>
      </div>

      {/* Brutalist Scroll Indicator */}
      <div className="absolute bottom-8 left-8">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-primary" style={{ backgroundColor: 'var(--color-primary)' }}></div>
          <div className="text-xs font-medium uppercase tracking-wide mono" style={{ color: 'var(--text-secondary)' }}>
            SCROLL DOWN
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
