import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Code, Share2, Palette, Smartphone, Search, TrendingUp } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

const Services = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);

  const services = [
    {
      icon: Code,
      title: 'Web Development',
      description: 'Custom websites and web applications built with modern technologies, responsive design, and optimal performance.',
      features: ['React & Next.js', 'E-commerce Solutions', 'Progressive Web Apps', 'API Integration'],
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Share2,
      title: 'Social Media Management',
      description: 'Strategic social media campaigns that build communities, increase engagement, and drive meaningful conversions.',
      features: ['Content Strategy', 'Community Management', 'Paid Advertising', 'Analytics & Reporting'],
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Palette,
      title: 'Brand Development',
      description: 'Complete brand identity solutions that resonate with your target audience and differentiate your business.',
      features: ['Logo Design', 'Brand Guidelines', 'Visual Identity', 'Brand Strategy'],
      color: 'from-orange-500 to-red-500'
    },
    {
      icon: Smartphone,
      title: 'Mobile App Development',
      description: 'Native and cross-platform mobile applications that deliver exceptional user experiences across all devices.',
      features: ['iOS & Android', 'React Native', 'UI/UX Design', 'App Store Optimization'],
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Search,
      title: 'SEO Optimization',
      description: 'Comprehensive SEO strategies that improve your search rankings and drive organic traffic to your website.',
      features: ['Technical SEO', 'Content Optimization', 'Local SEO', 'Performance Monitoring'],
      color: 'from-indigo-500 to-blue-500'
    },
    {
      icon: TrendingUp,
      title: 'Digital Marketing',
      description: 'Data-driven marketing campaigns that maximize ROI and accelerate your business growth in the digital landscape.',
      features: ['PPC Advertising', 'Email Marketing', 'Conversion Optimization', 'Marketing Automation'],
      color: 'from-teal-500 to-cyan-500'
    }
  ];

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    const cards = cardsRef.current;

    if (!section || !title || !cards) return;

    // Animate title
    gsap.fromTo(title.children, 
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        stagger: 0.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: title,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Animate service cards
    gsap.fromTo(cards.children,
      { opacity: 0, y: 80, scale: 0.9 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger: 0.15,
        ease: "power3.out",
        scrollTrigger: {
          trigger: cards,
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Cleanup
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section ref={sectionRef} id="services" className="section-padding" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      <div className="container-custom">
        <div ref={titleRef} className="space-y-8 md:space-y-12 lg:space-y-16 mb-16 md:mb-20 lg:mb-24">
          <div className="inline-block p-4 border-4 border-secondary" style={{ borderColor: 'var(--color-secondary)', backgroundColor: 'var(--color-secondary)' }}>
            <span className="text-black font-bold text-lg mono uppercase tracking-wide">
              SERVICES
            </span>
          </div>
          <h2 className="text-6xl md:text-8xl lg:text-9xl font-bold leading-none" style={{ fontFamily: 'Space Grotesk, sans-serif', color: 'var(--text-primary)', lineHeight: 'var(--leading-none)' }}>
            WHAT WE
            <br />
            <span className="gradient-text">BUILD</span>
          </h2>
          <div className="max-w-4xl">
            <p className="text-xl md:text-2xl lg:text-3xl font-medium" style={{ color: 'var(--text-secondary)', lineHeight: 'var(--leading-normal)' }}>
              WE DON'T DO EVERYTHING. WE DO THESE THINGS REALLY WELL.
              <br />
              NO FLUFF. NO BS. JUST RESULTS.
            </p>
          </div>
        </div>
        
        <div ref={cardsRef} className="grid-brutal">
          {services.map((service, index) => {
            const Icon = service.icon;
            const colors = [
              { bg: 'var(--color-primary)', border: 'var(--color-primary)' },
              { bg: 'var(--color-secondary)', border: 'var(--color-secondary)' },
              { bg: 'var(--color-accent)', border: 'var(--color-accent)' },
              { bg: 'var(--color-primary)', border: 'var(--color-primary)' },
              { bg: 'var(--color-secondary)', border: 'var(--color-secondary)' },
              { bg: 'var(--color-accent)', border: 'var(--color-accent)' }
            ];
            const color = colors[index % colors.length];

            return (
              <div key={index} className="card card-padding group h-full flex flex-col">
                <div className="flex items-start gap-6 mb-6 md:mb-8">
                  <div
                    className="w-16 h-16 md:w-20 md:h-20 border-4 flex items-center justify-center group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200"
                    style={{ borderColor: color.border, backgroundColor: color.bg }}
                  >
                    <Icon size={28} className="text-black md:w-8 md:h-8" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold uppercase" style={{ fontFamily: 'Space Grotesk, sans-serif', color: 'var(--text-primary)', lineHeight: 'var(--leading-tight)' }}>
                      {service.title}
                    </h3>
                  </div>
                </div>

                <p className="mb-6 md:mb-8 text-lg md:text-xl font-medium flex-grow" style={{ color: 'var(--text-secondary)', lineHeight: 'var(--leading-normal)' }}>
                  {service.description}
                </p>

                <ul className="text-sm md:text-base mb-8 md:mb-10 space-y-3" style={{ color: 'var(--text-muted)' }}>
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3 md:gap-4">
                      <span className="w-2 h-2 md:w-3 md:h-3 mt-2 flex-shrink-0" style={{ backgroundColor: color.bg }}></span>
                      <span className="mono uppercase tracking-wide font-medium">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button className="btn btn-outline w-full mt-auto">
                  LEARN MORE
                </button>
              </div>
            );
          })}
        </div>

        {/* Brutalist CTA Section */}
        <div className="mt-20">
          <div className="card card-padding" style={{ backgroundColor: 'var(--color-primary)', borderColor: 'var(--color-primary)' }}>
            <div className="grid-asymmetric items-center">
              <div>
                <h3 className="text-4xl md:text-5xl font-bold mb-6 text-black uppercase" style={{ fontFamily: 'Space Grotesk, sans-serif' }}>
                  READY TO
                  <br />
                  START?
                </h3>
                <p className="text-lg text-black/80 mb-8 leading-tight">
                  STOP WASTING TIME WITH AGENCIES THAT DON'T GET IT.
                  <br />
                  LET'S BUILD SOMETHING THAT ACTUALLY WORKS.
                </p>
              </div>
              <div className="flex flex-col gap-4">
                <button
                  className="btn btn-lg text-black border-black hover:bg-black hover:text-primary"
                  style={{ backgroundColor: 'transparent', borderColor: 'black', color: 'black' }}
                  onClick={() => window.open('https://wa.me/94715768552', '_blank')}
                >
                  START PROJECT
                </button>
                <button
                  className="btn btn-lg text-black border-black hover:bg-black hover:text-primary"
                  style={{ backgroundColor: 'transparent', borderColor: 'black', color: 'black' }}
                >
                  VIEW PROCESS
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
